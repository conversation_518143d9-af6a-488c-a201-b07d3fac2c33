<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Temperature Icon Generator</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        background-color: #f0f0f0;
      }
      .container {
        display: flex;
        gap: 40px;
        margin-top: 20px;
        flex-wrap: wrap;
        justify-content: center;
      }
      .controls {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .controls label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
      }
      .controls input[type='number'],
      .controls select {
        width: 100%;
        padding: 8px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      .controls input[type='checkbox'] {
        margin-right: 10px;
      }
      .controls .checkbox-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
      }
      #temperatureIcon {
        border: 1px solid #ccc;
        background-color: #333; /* Simulate dark background for SVG */
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #333;
      }
    </style>
  </head>
  <body>
    <h1>Temperature Icon Generator</h1>

    <div class="container">
      <div class="controls">
        <h2>Parameters</h2>
        <label for="waterTemp">Current Water Temperature (°C):</label>
        <input
          type="number"
          id="waterTemp"
          value="104.9"
          step="0.1"
          oninput="updateImage()"
        />

        <label for="targetTemp"
          >Target Temperature (°C, leave empty for no target):</label
        >
        <input
          type="number"
          id="targetTemp"
          value="104"
          step="0.1"
          oninput="updateImage()"
        />

        <label for="unit">Unit:</label>
        <select id="unit" onchange="updateImage()">
          <option value="C">Celsius (°C)</option>
          <option value="F">Fahrenheit (°F)</option>
        </select>

        <div class="checkbox-group">
          <input
            type="checkbox"
            id="heaterState"
            checked
            onchange="updateImage()"
          />
          <label for="heaterState">Heater ON</label>
        </div>

        <div class="checkbox-group">
          <input
            type="checkbox"
            id="powerState"
            checked
            onchange="updateImage()"
          />
          <label for="powerState">Power ON (Display Green/Red)</label>
        </div>

        <button
          onclick="updateImage(true)"
          style="
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          Refresh Timestamp
        </button>
      </div>

      <div>
        <h2>Generated Icon</h2>
        <img id="temperatureIcon" src="" alt="Temperature Icon" />
      </div>
    </div>

    <script>
      // Helper function to convert temperature
      // Assumes input 'temp' is in Celsius
      function convertTemperature(temp, unit) {
        if (unit.toUpperCase() === 'C') {
          return parseFloat(temp).toFixed(1);
        } else if (unit.toUpperCase() === 'F') {
          return ((parseFloat(temp) * 9) / 5 + 32).toFixed(1);
        }
        return parseFloat(temp).toFixed(1); // Fallback
      }

      // Adapted function from your provided code
      async function drawTemperatureIcon(status, unit, timestamp) {
        const currentTemp = convertTemperature(
          status.water_temperature || 0,
          unit
        );
        // Handle targetTemp being optional (empty input field)
        const targetTemp =
          status.temperature_setting !== null
            ? convertTemperature(status.temperature_setting, unit)
            : null;
        const heaterStatus = status.heater_state
          ? 'Heating: ON'
          : 'Heating: OFF';
        const unitSymbol = unit.charAt(0).toUpperCase();

        // Determine if hot tub is "powered on" based on actual data structure
        // Check if online and any major systems are active
        const isPoweredOn =
          status.is_online &&
          (status.heater_state ||
            status.filter_state ||
            status.bubble_state ||
            status.jet_state ||
            status.uvc_state ||
            status.ozone_state);

        // Add timestamp for unique image generation
        const now = timestamp || Date.now();
        const timeString = new Date(now).toLocaleTimeString('en-US', {
          hour12: true,
          hour: '2-digit',
          minute: '2-digit',
        });

        // Use simple SVG for icon generation
        const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
          <rect width="144" height="144" fill="#1a1a1a" />

          <!-- Heater Icon -->
          <g transform="translate(15, 8)">
            <path 
              fill="none" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              d="M16,4
                C16,2.343 14.657,1 13,1
                C11.343,1 10,2.343 10,4
                V20
                C10,21.657 11.343,23 13,23
                C14.657,23 16,21.657 16,20
                V4
                Z" 
            />
            <circle 
              cx="13" 
              cy="22" 
              r="2" 
              fill="${status.heater_state ? '#00ff00' : '#ff4444'}" 
            />
            <rect 
              x="11" 
              y="16" 
              width="4" 
              height="5" 
              fill="${status.heater_state ? '#00ff00' : '#ff4444'}" 
            />
            <line 
              x1="11" 
              y1="8" 
              x2="15" 
              y2="8" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <line 
              x1="11" 
              y1="12" 
              x2="15" 
              y2="12" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <line 
              x1="11" 
              y1="16" 
              x2="15" 
              y2="16" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
          </g>

          <!-- Filter Icon -->
          <g transform="translate(53.5, -1) scale(1, 1.35)">
            <path 
              d="M10,12 C10,10 12,8 16,8 C20,8 22,10 22,12 V20 C22,22 20,24 16,24 C12,24 10,22 10,20 Z" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
            />
            <path 
              d="M4,12 C5,11 6,13 7,12 C8,11 9,13 10,12" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M4,16 C5,15 6,17 7,16 C8,15 9,17 10,16" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M4,20 C5,19 6,21 7,20 C8,19 9,21 10,20" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M22,14 C23,13 24,15 25,14 C26,13 27,15 28,14" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M22,18 C23,17 24,19 25,18 C26,17 27,19 28,18" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
          </g>

          <!-- UVC Light Icon -->
          <g transform="translate(99, 0) scale(1, 1.3)">
            <circle 
              cx="16" 
              cy="20" 
              r="4" 
              fill="none" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
            />
            <line 
              x1="16" 
              y1="12" 
              x2="16" 
              y2="8" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="12" 
              y1="13" 
              x2="10" 
              y2="10" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="20" 
              y1="13" 
              x2="22" 
              y2="10" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="11" 
              y1="16" 
              x2="8" 
              y2="16" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="21" 
              y1="16" 
              x2="24" 
              y2="16" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <path 
              d="M14,20 C14,19 15,18 16,18 C17,18 18,19 18,20" 
              fill="none" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
          </g>

          <text 
            x="72" 
            y="75" 
            text-anchor="middle" 
            fill="${status.heater_state ? '#00ff00' : '#888888'}" 
            font-family="Arial" 
            font-size="36" 
            font-weight="bold"
          >
            ${currentTemp}°${unitSymbol}
          </text>
          ${
            targetTemp
              ? `<text 
                  x="72" 
                  y="101" 
                  text-anchor="middle" 
                  fill="#ffffff" 
                  font-family="Arial" 
                  font-size="18"
                >
                  Target: ${targetTemp}°${unitSymbol}
                </text>`
              : ''
          }
          <text 
            x="72" 
            y="135" 
            text-anchor="middle" 
            fill="#666666" 
            font-family="Arial" 
            font-size="21"
          >
            ${timeString}
          </text>
          <!-- Update: ${now} -->
		</svg>`;

        // Convert SVG to data URL (browser compatible way)
        // btoa: base64 encode
        // encodeURIComponent: handles special characters safely
        // unescape: decodes URIs created by encodeURIComponent (less common but good practice for SVG text)
        const dataURL = `data:image/svg+xml;base64,${btoa(
          unescape(encodeURIComponent(svg))
        )}`;

        // Simulate action.setImage
        document.getElementById('temperatureIcon').src = dataURL;

        console.log(
          `Updating image: ${currentTemp}° (${heaterStatus}) at ${timeString} - Powered: ${isPoweredOn}`
        );
      }

      // Function to read inputs and update the image
      function updateImage(refreshTimestamp = false) {
        const waterTempInput = document.getElementById('waterTemp');
        const targetTempInput = document.getElementById('targetTemp');
        const unitSelect = document.getElementById('unit');
        const heaterStateCheckbox = document.getElementById('heaterState');
        const powerStateCheckbox = document.getElementById('powerState');

        // Create status object matching actual hot tub data structure
        const status = {
          water_temperature: parseFloat(waterTempInput.value) || 0,
          temperature_setting:
            targetTempInput.value !== ''
              ? parseFloat(targetTempInput.value)
              : null,
          heater_state: heaterStateCheckbox.checked ? 1 : 0,
          is_online: powerStateCheckbox.checked,
          // Add other states that would be present in real data
          filter_state: powerStateCheckbox.checked ? 1 : 0,
          bubble_state: 0,
          jet_state: powerStateCheckbox.checked ? 1 : 0,
          uvc_state: powerStateCheckbox.checked ? 1 : 0,
          ozone_state: 0,
        };
        const unit = unitSelect.value;
        const timestamp = refreshTimestamp ? Date.now() : null; // Pass null to use Date.now() inside the function by default

        drawTemperatureIcon(status, unit, timestamp);
      }

      // Initial image draw on page load
      document.addEventListener('DOMContentLoaded', updateImage);
    </script>
  </body>
</html>
